<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request History - Faculty Portal | INES-Ruhengeri Transcript System</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        .profile-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #007bff;
            margin-bottom: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .sidebar-header h2 {
            color: #fff;
            font-size: 1.2rem;
            margin: 10px 0 5px;
            font-weight: 600;
        }
        .sidebar-header p {
            color: #fff;
            font-size: 0.9rem;
            margin: 0;
        }
        .dashboard-header h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        .dashboard-header p {
            color: #666;
            font-size: 1rem;
        }
        .completed-icon {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }
        .approved-icon {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }
        .text-muted {
            color: #6c757d !important;
        }
        .action-buttons {
            display: flex;
            gap: 5px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Faculty Profile" class="profile-image">
                <h2>{{ session.name }}</h2>
                <p>Faculty ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('faculty_dashboard') }}" class="active">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                            {% if pending_count > 0 %}
                                <span class="badge">{{ pending_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('upload_transcript') }}">
                            <i class="fas fa-upload"></i> Upload Transcript
                            {% if pending_count > 0 %}
                                <span class="badge">{{ pending_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('request_history') }}">
                            <i class="fas fa-history"></i> Request History
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <div class="top-bar">
                <button class="menu-toggle" id="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <div class="dashboard-header">
                <h1>Request History</h1>
                <p>View complete history of uploaded transcript requests</p>
            </div>

            <div class="stats-container" style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <div class="stat-card">
                    <div class="stat-icon approved-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Total Uploaded</h3>
                        <p class="stat-number">{{ total_uploaded or 0 }}</p>
                    </div>
                </div>

                <!-- Print Report Button in Right Corner -->
                <div>
                    <button type="button" class="btn btn-info" onclick="printTranscriptReport()" style="padding: 12px 24px; border-radius: 8px; font-size: 16px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        <i class="fas fa-print"></i> Print Transcript Report
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h2>Uploaded Transcript History</h2>

                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>Request ID</th>
                                    <th>Student Name</th>
                                    <th>Academic Year</th>
                                    <th>Upload Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if requests %}
                                    {% for request in requests %}
                                        <tr>
                                            <td>{{ request.id }}</td>
                                            <td>{{ request.student_name }}</td>
                                            <td>{{ request.academic_years|join(', ') if request.academic_years is iterable and request.academic_years is not string else request.academic_years }}</td>
                                            <td>{{ request.completed_at.strftime('%Y-%m-%d') if request.completed_at else (request.faculty_processed_at.strftime('%Y-%m-%d') if request.faculty_processed_at else 'N/A') }}</td>
                                            <td style="text-align: center;">
                                                <img src="{{ url_for('static', filename='images/done.webp') }}" alt="Done" style="width: 64px; height: 64px; vertical-align: middle;" title="Request has been issued">
                                            </td>
                                            <td style="text-align: center;">
                                                <div class="action-buttons">
                                                    <form method="POST" action="{{ url_for('faculty_delete_request_from_history', request_id=request.id) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this uploaded request?')">
                                                        <button type="submit" class="btn btn-danger" style="padding: 8px 12px; border-radius: 6px;" title="Delete uploaded request">
                                                            <img src="{{ url_for('static', filename='images/delete.png') }}" alt="Delete" style="width: 20px; height: 20px;">
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="6" style="text-align: center;">
                                            <div class="text-center py-4">
                                                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                                <p class="text-muted">No uploaded transcripts found</p>
                                                <a href="{{ url_for('upload_transcript') }}" class="btn btn-primary">
                                                    <i class="fas fa-upload"></i> Upload First Transcript
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Quick Actions</h2>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-col">
                            <a href="{{ url_for('upload_transcript') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-upload"></i> Upload Transcripts
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Clear All Requests Modal -->
    <div class="modal fade" id="facultyClearAllModal" tabindex="-1" aria-labelledby="facultyClearAllModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="facultyClearAllModalLabel">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        Clear All Faculty History
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <strong>⚠️ Warning:</strong> This action cannot be undone!
                    </div>
                    <p>Are you sure you want to delete <strong>all completed transcript uploads</strong>?</p>
                    <p class="text-muted">
                        <small>
                            • All completed and downloaded transcript records will be deleted<br>
                            • Pending uploads will be kept for processing<br>
                            • This will clear the faculty history
                        </small>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <form method="POST" action="{{ url_for('faculty_clear_all_requests') }}" style="display: inline;">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash-alt"></i> Clear All
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Toggle sidebar on mobile
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
        
        // Close alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        });

        // Print Transcript Report Function
        function printTranscriptReport() {
            console.log('🖨️ Print Transcript Report button clicked');

            // Get current date and time
            const now = new Date();
            const reportDate = now.toLocaleDateString();
            const reportTime = now.toLocaleTimeString();

            // Count uploaded transcripts
            const uploadedCount = {{ requests|length }};

            // Create detailed transcript report
            let transcriptRows = '';
            {% if requests %}
                {% for request in requests %}
                transcriptRows += `
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">{{ request.id }}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">{{ request.student_name }}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">{{ request.academic_years|join(', ') if request.academic_years is iterable and request.academic_years is not string else request.academic_years }}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">{{ request.completed_at.strftime('%Y-%m-%d') if request.completed_at else (request.faculty_processed_at.strftime('%Y-%m-%d') if request.faculty_processed_at else 'N/A') }}</td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">
                            <span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">
                                {{ request.status|title }}
                            </span>
                        </td>
                        <td style="padding: 8px; border: 1px solid #ddd;">{{ request.download_count or 0 }}</td>
                    </tr>
                `;
                {% endfor %}
            {% endif %}

            const reportContent = `
                <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 1000px; margin: 0 auto;">
                    <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #1a237e; padding-bottom: 20px;">
                        <h1 style="color: #1a237e; margin: 0; font-size: 28px;">INES-Ruhengeri</h1>
                        <h2 style="color: #333; margin: 10px 0; font-size: 22px;">Transcript Upload Report</h2>
                        <p style="color: #666; margin: 5px 0;"><strong>Generated on:</strong> ${reportDate} at ${reportTime}</p>
                        <p style="color: #666; margin: 5px 0;"><strong>Faculty:</strong> {{ session.name }}</p>
                        <p style="color: #666; margin: 5px 0;"><strong>Department:</strong> {{ session.department }}</p>
                    </div>



                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #1a237e; border-bottom: 2px solid #1a237e; padding-bottom: 10px; margin-bottom: 15px;">Uploaded Transcript Details</h3>
                        <table style="width: 100%; border-collapse: collapse; margin-top: 15px; font-size: 14px;">
                            <thead>
                                <tr style="background-color: #1a237e; color: white;">
                                    <th style="padding: 12px 8px; border: 1px solid #ddd; text-align: left;">Request ID</th>
                                    <th style="padding: 12px 8px; border: 1px solid #ddd; text-align: left;">Student Name</th>
                                    <th style="padding: 12px 8px; border: 1px solid #ddd; text-align: left;">Academic Years</th>
                                    <th style="padding: 12px 8px; border: 1px solid #ddd; text-align: left;">Upload Date</th>
                                    <th style="padding: 12px 8px; border: 1px solid #ddd; text-align: center;">Status</th>
                                    <th style="padding: 12px 8px; border: 1px solid #ddd; text-align: center;">Downloads</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${transcriptRows || '<tr><td colspan="6" style="padding: 20px; text-align: center; color: #666;">No uploaded transcripts found</td></tr>'}
                            </tbody>
                        </table>
                    </div>

                    <div style="margin-top: 40px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #eee; padding-top: 20px;">
                        <p>This report was generated from the INES-Ruhengeri Transcript Management System</p>
                        <p>© ${new Date().getFullYear()} INES-Ruhengeri. All rights reserved.</p>
                        <p><strong>Note:</strong> This report contains confidential student information and should be handled securely.</p>
                    </div>
                </div>
            `;

            // Open print window
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Transcript Upload Report - INES-Ruhengeri</title>
                    <style>
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                        body { font-family: Arial, sans-serif; }
                    </style>
                </head>
                <body>
                    ${reportContent}
                    <div class="no-print" style="text-align: center; margin-top: 20px;">
                        <button onclick="window.print()" style="padding: 10px 20px; background: #1a237e; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                            <i class="fas fa-print"></i> Print Report
                        </button>
                        <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            Close Window
                        </button>
                    </div>
                </body>
                </html>
            `);
            printWindow.document.close();

            console.log('✅ Transcript report window opened successfully');
        }

        // Faculty Clear All Modal Function
        function showFacultyClearAllModal() {
            console.log('🗑️ Faculty Clear All button clicked');

            const modalElement = document.getElementById('facultyClearAllModal');
            if (!modalElement) {
                console.error('❌ Modal element not found');
                alert('Error: Modal not found. Please refresh the page and try again.');
                return;
            }

            try {
                const clearAllModal = new bootstrap.Modal(modalElement);
                clearAllModal.show();
                console.log('✅ Faculty modal should be visible now');
            } catch (error) {
                console.error('❌ Error creating/showing modal:', error);
                const confirmed = confirm('Are you sure you want to delete all completed transcript uploads? This action cannot be undone.');
                if (confirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '{{ url_for("faculty_clear_all_requests") }}';
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }
    </script>
</body>
</html>
