<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance Dashboard | INES-Ruhengeri Transcript System</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        .profile-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #1976D2;
            margin-bottom: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .sidebar-header h2 {
            color: #fff;
            font-size: 1.2rem;
            margin: 10px 0 5px;
            font-weight: 600;
        }
        .sidebar-header p {
            color: #fff;
            font-size: 0.9rem;
            margin: 0;
        }
        .dashboard-header h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        .dashboard-header p {
            color: #666;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Finance Profile" class="profile-image">
                <h2>{{ session.name }}</h2>
                <p>Finance ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('finance_dashboard') }}" class="active">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                            {% if approved_count > 0 or rejected_count > 0 %}
                                <span class="badge">{{ approved_count + rejected_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('view_status') }}">
                            <i class="fas fa-eye"></i> View Status
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('view_history') }}">
                            <i class="fas fa-history"></i> View History
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manage_fees') }}">
                            <i class="fas fa-money-bill-wave"></i> Manage Fees
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <div class="top-bar">
                <button class="menu-toggle" id="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <div class="dashboard-header">
                <h1>Welcome, {{ session.name }}</h1>
                <p>Finance Management Dashboard</p>
            </div>
            
            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-icon pending-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Pending Requests</h3>
                        <p class="stat-number">{{ pending_count }}</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon approved-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Approved Requests</h3>
                        <p class="stat-number">{{ approved_count }}</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon rejected-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Rejected Requests</h3>
                        <p class="stat-number">{{ rejected_count }}</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h2>Recent Processed Requests</h2>
                    <div style="display: flex; gap: 10px;">
                        {% if requests %}
                        <button type="button" class="btn btn-sm btn-danger" onclick="showFinanceClearAllModal()">
                            <i class="fas fa-trash-alt"></i> Clear All
                        </button>
                        {% endif %}
                        <a href="{{ url_for('view_history') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i> View All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>Request ID</th>
                                    <th>Student Name</th>
                                    <th>Date</th>
                                    <th>Academic Years</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if requests %}
                                    {% for request in requests %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ request.student_name }}</td>
                                            <td>{{ request.date }}</td>
                                            <td>{{ request.academic_years|join(', ') }}</td>
                                            <td>{{ request.total_price }} RWF</td>
                                            <td>
                                                {% if request.status == 'pending' %}
                                                    <img src="{{ url_for('static', filename='images/13.png') }}" alt="Pending" style="width: 20px; height: 20px; vertical-align: middle;" title="Pending">
                                                {% elif request.status == 'approved' %}
                                                    <img src="{{ url_for('static', filename='images/approved.png') }}" alt="Approved" style="width: 20px; height: 20px; vertical-align: middle;" title="Approved">
                                                {% elif request.status == 'rejected' %}
                                                    <img src="{{ url_for('static', filename='images/rejected.png') }}" alt="Rejected" style="width: 20px; height: 20px; vertical-align: middle;" title="Rejected">
                                                {% elif request.status == 'completed' %}
                                                    <img src="{{ url_for('static', filename='images/8.png') }}" alt="Ready for Download" style="width: 20px; height: 20px; vertical-align: middle;" title="Ready for Download">
                                                {% elif request.status == 'done' %}
                                                    <img src="{{ url_for('static', filename='images/done.png') }}" alt="Downloaded" style="width: 20px; height: 20px; vertical-align: middle;" title="Downloaded">
                                                {% else %}
                                                    <img src="{{ url_for('static', filename='images/13.png') }}" alt="Processing" style="width: 20px; height: 20px; vertical-align: middle;" title="Processing">
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="6" style="text-align: center;">
                                            <div class="text-center py-4">
                                                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                                <p class="text-muted">No recent requests</p>
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Quick Actions</h2>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-col">
                            <a href="{{ url_for('view_status') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-eye"></i> View Request Status
                            </a>
                        </div>
                        <div class="form-col">
                            <a href="{{ url_for('view_history') }}" class="btn btn-secondary btn-block">
                                <i class="fas fa-history"></i> View Request History
                            </a>
                        </div>
                        <div class="form-col">
                            <a href="{{ url_for('manage_fees') }}" class="btn btn-info btn-block">
                                <i class="fas fa-money-bill-wave"></i> Manage Department Fees
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Clear All Requests Modal -->
    <div class="modal fade" id="financeClearAllModal" tabindex="-1" aria-labelledby="financeClearAllModalLabel" aria-hidden="true" style="z-index: 9999;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                <div class="modal-header" style="background: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                    <h5 class="modal-title" id="financeClearAllModalLabel" style="color: #495057; font-weight: 600;">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        Clear All Finance Records
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="font-size: 1.2rem;"></button>
                </div>
                <div class="modal-body" style="padding: 25px;">
                    <div class="alert alert-warning" style="border-left: 4px solid #ffc107;">
                        <strong>⚠️ Warning:</strong> This action cannot be undone!
                    </div>
                    <p style="font-size: 1.1rem; margin-bottom: 15px;">Are you sure you want to delete <strong>all processed requests</strong>?</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;">
                        <p class="mb-2"><strong>What will happen:</strong></p>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li>All approved, rejected, and completed requests will be deleted</li>
                            <li>Pending requests will be kept for processing</li>
                            <li>This will clear the finance history</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer" style="background: #f8f9fa; border-top: 1px solid #dee2e6; padding: 15px 25px;">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="padding: 10px 20px; font-weight: 500;">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <form method="POST" action="{{ url_for('finance_clear_all_requests') }}" style="display: inline; margin-left: 10px;">
                        <button type="submit" class="btn btn-danger" style="padding: 10px 20px; font-weight: 500;">
                            <i class="fas fa-trash-alt"></i> Clear All
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Toggle sidebar on mobile
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
        
        // Close alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        });

        // Finance Clear All Modal Function
        function showFinanceClearAllModal() {
            console.log('🗑️ Finance Clear All button clicked');

            const modalElement = document.getElementById('financeClearAllModal');
            if (!modalElement) {
                console.error('❌ Modal element not found');
                alert('Error: Modal not found. Please refresh the page and try again.');
                return;
            }

            try {
                // Try Bootstrap 5 first
                if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    const clearAllModal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static',
                        keyboard: false
                    });
                    clearAllModal.show();
                    console.log('✅ Bootstrap 5 finance modal shown successfully');
                    return;
                }

                // Try jQuery/Bootstrap 4 fallback
                if (typeof $ !== 'undefined' && $.fn.modal) {
                    $(modalElement).modal('show');
                    console.log('✅ jQuery finance modal shown successfully');
                    return;
                }

                // Manual modal display fallback
                console.log('⚠️ No modal library available, showing manually');
                modalElement.style.display = 'block';
                modalElement.classList.add('show');
                modalElement.setAttribute('aria-hidden', 'false');

                // Add backdrop
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'financeModalBackdrop';
                document.body.appendChild(backdrop);

                // Prevent body scroll
                document.body.style.overflow = 'hidden';

                console.log('✅ Manual finance modal display successful');

            } catch (error) {
                console.error('❌ Error creating/showing modal:', error);
                const confirmed = confirm('Are you sure you want to delete all processed requests? This action cannot be undone.');
                if (confirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '{{ url_for("finance_clear_all_requests") }}';
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }

        // Function to hide finance modal manually
        function hideFinanceClearAllModal() {
            const modalElement = document.getElementById('financeClearAllModal');
            const backdrop = document.getElementById('financeModalBackdrop');

            if (modalElement) {
                modalElement.style.display = 'none';
                modalElement.classList.remove('show');
                modalElement.setAttribute('aria-hidden', 'true');
            }

            if (backdrop) {
                backdrop.remove();
            }

            // Restore body scroll
            document.body.style.overflow = '';
        }

        // Add event listeners when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Make sure modal buttons are clickable
            const modal = document.getElementById('financeClearAllModal');
            if (modal) {
                // Close button functionality
                const closeButtons = modal.querySelectorAll('[data-bs-dismiss="modal"], .btn-close');
                closeButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        hideFinanceClearAllModal();
                    });
                });

                // Form submission
                const form = modal.querySelector('form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        console.log('Finance form submitted');
                        // Let the form submit normally
                    });
                }
            }

            // Backdrop click to close
            document.addEventListener('click', function(e) {
                if (e.target && e.target.id === 'financeModalBackdrop') {
                    hideFinanceClearAllModal();
                }
            });
        });
    </script>
</body>
</html>
