<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faculty Dashboard | INES-Ruhengeri Transcript System</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        .profile-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #1976D2;
            margin-bottom: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .sidebar-header h2 {
            color: #fff;
            font-size: 1.2rem;
            margin: 10px 0 5px;
            font-weight: 600;
        }
        .sidebar-header p {
            color: #fff;
            font-size: 0.9rem;
            margin: 0;
        }
        .dashboard-header h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        .dashboard-header p {
            color: #666;
            font-size: 1rem;
        }

        /* Enhanced Faculty Dashboard Styles */
        .action-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .action-buttons .btn {
            transition: all 0.3s ease;
            font-weight: 600;
            border-radius: 20px;
            padding: 8px 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .action-buttons .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-badge img {
            width: 18px !important;
            height: 18px !important;
        }

        /* Status-specific styling */
        .status-badge.status-pending {
            background: rgba(255, 193, 7, 0.1);
            color: #856404;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .status-badge.status-completed {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status-badge.status-done {
            background: rgba(23, 162, 184, 0.1);
            color: #0c5460;
            border: 1px solid rgba(23, 162, 184, 0.3);
        }

        /* Table improvements */
        .table th, table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            padding: 12px;
        }

        .table td, table td {
            padding: 12px;
            vertical-align: middle;
        }

        .table tbody tr:hover, table tbody tr:hover {
            background: #f8f9fa;
        }

        /* Badge styling for sidebar */
        .badge {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 0.7rem;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Faculty Profile" class="profile-image">
                <h2>{{ session.name }}</h2>
                <p>Faculty ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('faculty_dashboard') }}" class="active">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                            {% if pending_count > 0 %}
                                <span class="badge">{{ pending_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('upload_transcript') }}">
                            <i class="fas fa-upload"></i> Upload Transcript
                            {% if pending_count > 0 %}
                                <span class="badge">{{ pending_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('request_history') }}">
                            <i class="fas fa-history"></i> Request History
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <div class="top-bar">
                <button class="menu-toggle" id="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <div class="dashboard-header">
                <h1>Welcome, {{ session.name }}</h1>
                <p>Manage academic transcript uploads and approvals</p>
            </div>
            
            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-icon pending-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Pending Uploads</h3>
                        <p class="stat-number">{{ pending_count }}</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon completed-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>Total Uploaded</h3>
                        <p class="stat-number">{{ completed_count }}</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Pending Transcript Uploads</h2>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>Request ID</th>
                                    <th>Student Name</th>
                                    <th>Date</th>
                                    <th>Academic Years</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if requests %}
                                    {% for request in requests %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ request.student_name }}</td>
                                            <td>{{ request.date }}</td>
                                            <td>{{ request.academic_years|join(', ') }}</td>
                                            <td>
                                                <span class="status-badge status-pending">
                                                    <img src="{{ url_for('static', filename='images/13.png') }}" alt="Pending Upload" style="width: 20px; height: 20px; vertical-align: middle;">
                                                    Pending Upload
                                                </span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="{{ url_for('upload_transcript') }}" class="btn btn-sm btn-success"
                                                       style="background: linear-gradient(135deg, #28a745, #20c997); border: none; padding: 8px 16px; border-radius: 20px; font-weight: 600; text-decoration: none; color: white; display: inline-flex; align-items: center; gap: 6px; transition: all 0.3s ease;">
                                                        <i class="fas fa-upload"></i> Upload Transcript
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="5" style="text-align: center;">No pending transcript uploads</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Quick Actions</h2>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-col">
                            <a href="{{ url_for('upload_transcript') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-upload"></i> Upload Transcripts
                            </a>
                        </div>
                        <div class="form-col">
                            <a href="{{ url_for('request_history') }}" class="btn btn-secondary btn-block">
                                <i class="fas fa-history"></i> View Request History
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>



    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Toggle sidebar on mobile
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
        
        // Close alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        });


    </script>
</body>
</html>
