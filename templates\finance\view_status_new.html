{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Finance Profile" class="profile-image">
                <h3>{{ session.name }}</h3>
                <p>Finance Officer</p>
                <p style="font-size: 0.8rem; opacity: 0.8;">ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('finance_dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('view_status') }}" class="active">
                            <i class="fas fa-eye"></i> View Status
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('view_history') }}">
                            <i class="fas fa-history"></i> View History
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manage_fees') }}">
                            <i class="fas fa-money-bill-wave"></i> Manage Fees
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 main-content">
            <!-- Header -->
            <div class="content-header">
                <h1><i class="fas fa-eye"></i> View Status - Pending Requests</h1>
                <p class="subtitle">Review and process pending transcript requests</p>
            </div>

            <!-- Pending Requests -->
            <div class="data-table">
                <div class="table-header">
                    <h2><i class="fas fa-clock"></i> Pending Requests</h2>
                    <span>{{ pending_requests|length }} pending request(s)</span>
                </div>
                <div class="table-responsive">
                    {% if pending_requests %}
                    <table class="enhanced-table">
                        <thead>
                            <tr>
                                <th>Request ID</th>
                                <th>Student Name</th>
                                <th>Date</th>
                                <th>Academic Years</th>
                                <th>Amount</th>
                                <th>Payment Method</th>
                                <th>Proof</th>
                                <th>School Fees Paid</th>
                                <th>Total Fees Required</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in pending_requests %}
                            <tr>
                                <td><strong>{{ request.request_number or request.id }}</strong></td>
                                <td>{{ request.student_name }}</td>
                                <td>{{ request.date.strftime('%Y-%m-%d') if request.date else 'N/A' }}</td>
                                <td>{{ request.academic_years|join(', ') if request.academic_years else 'N/A' }}</td>
                                <td class="amount-cell">{{ "{:,.0f}".format(request.total_amount) }} RWF</td>
                                <td>{{ request.payment_method|title }}</td>
                                <td>
                                    {% if request.payment_proof_filename %}
                                    <a href="{{ url_for('view_payment_proof', filename=request.payment_proof_filename) }}" 
                                       class="proof-link" target="_blank">
                                        <i class="fas fa-image"></i> View Proof
                                    </a>
                                    {% else %}
                                    <span style="color: #999;">No proof</span>
                                    {% endif %}
                                </td>
                                <td class="amount-cell">{{ "{:,.0f}".format(request.school_fees_paid or 0) }} RWF</td>
                                <td class="amount-cell">{{ "{:,.0f}".format(request.total_fees_required or 0) }} RWF</td>
                                <td>
                                    <span class="status-badge pending">
                                        <img src="{{ url_for('static', filename='images/13.png') }}" alt="Pending" style="width: 16px; height: 16px;">
                                        Pending
                                    </span>
                                </td>
                                <td>
                                    <form method="POST" action="{{ url_for('finance_approve_request') }}" style="display: inline;">
                                        <input type="hidden" name="request_id" value="{{ request.id }}">
                                        <button type="submit" class="action-btn btn-approve" 
                                                onclick="return confirm('Are you sure you want to approve this request?')">
                                            <i class="fas fa-check"></i> Approve
                                        </button>
                                    </form>
                                    <button type="button" class="action-btn btn-reject"
                                            onclick="showRejectModal('{{ request.id }}', '{{ request.student_name }}')">
                                        <i class="fas fa-times"></i> Reject
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <div class="no-data">
                        <i class="fas fa-inbox"></i>
                        <h3>No Pending Requests</h3>
                        <p>All transcript requests have been processed.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">Reject Transcript Request</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="rejectForm" method="POST" action="{{ url_for('finance_reject_request') }}">
                <input type="hidden" name="request_id" id="rejectRequestId">
                <div class="modal-body">
                    <p id="rejectConfirmationText"></p>
                    <div class="mb-3">
                        <label for="rejectionReason" class="form-label">Reason for Rejection (Optional):</label>
                        <textarea class="form-control" id="rejectionReason" name="rejection_reason" rows="4"
                                  placeholder="Leave blank to auto-calculate school fees balance, or provide a custom reason..."></textarea>
                        <div class="form-text">
                            If left blank, the system will automatically calculate the school fees balance and provide a detailed reason.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Confirm Rejection</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Enhanced Finance Dashboard Styles */
.container-fluid {
    padding: 0;
    min-height: 100vh;
    background: #f5f7fa;
}

.sidebar {
    background: linear-gradient(135deg, #083464, #1976D2);
    color: white;
    min-height: 100vh;
    padding: 0;
}

.sidebar-header {
    text-align: center;
    padding: 30px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255, 255, 255, 0.3);
    margin-bottom: 15px;
}

.sidebar-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin: 5px 0;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: #4CAF50;
}

.sidebar-menu i {
    margin-right: 12px;
    width: 20px;
}

.main-content {
    padding: 30px;
}

.content-header {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.content-header h1 {
    color: #083464;
    font-size: 2rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
}

.data-table {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    background: linear-gradient(135deg, #083464, #1976D2);
    color: white;
    padding: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h2 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.table-responsive {
    overflow-x: auto;
}

.enhanced-table {
    width: 100%;
    border-collapse: collapse;
}

.enhanced-table th,
.enhanced-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.enhanced-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #083464;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.enhanced-table tr:hover {
    background: #f8f9fa;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.status-badge.pending {
    background: rgba(255, 152, 0, 0.1);
    color: #F57C00;
}

.action-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 3px;
}

.btn-approve {
    background: linear-gradient(135deg, #4CAF50, #388E3C);
    color: white;
}

.btn-reject {
    background: linear-gradient(135deg, #F44336, #D32F2F);
    color: white;
}

.btn-approve:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-reject:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.proof-link {
    color: #1976D2;
    text-decoration: none;
    font-weight: 600;
}

.proof-link:hover {
    text-decoration: underline;
}

.amount-cell {
    font-weight: 700;
    color: #4CAF50;
}

.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        transition: left 0.3s ease;
        z-index: 1000;
        width: 280px;
    }

    .sidebar.active {
        left: 0;
    }

    .main-content {
        margin-left: 0;
        padding: 20px;
    }
}
</style>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // Function to show rejection modal
    function showRejectModal(requestId, studentName) {
        const rejectModal = new bootstrap.Modal(document.getElementById('rejectModal'));
        document.getElementById('rejectConfirmationText').textContent =
            `Are you sure you want to reject the transcript request for ${studentName}?`;
        document.getElementById('rejectRequestId').value = requestId;
        document.getElementById('rejectionReason').value = ''; // Clear previous reason
        rejectModal.show();
    }

    // Auto-hide flash messages
    document.addEventListener('DOMContentLoaded', function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 300);
            }, 5000);
        });
    });
</script>

{% endblock %}
