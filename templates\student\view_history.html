{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Student Profile" class="profile-image">
                <h3>{{ session.name }}</h3>
                <p>Student Portal</p>
                <p style="font-size: 0.8rem; opacity: 0.8;">ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('student_dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('request_transcript') }}">
                            <i class="fas fa-plus-circle"></i> Request Transcript
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('student_view_status') }}">
                            <i class="fas fa-eye"></i> View Status
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('student_view_history') }}" class="active">
                            <i class="fas fa-history"></i> View History
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('view_downloads') }}">
                            <i class="fas fa-download"></i> Downloads
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 main-content">
            <!-- Header -->
            <div class="content-header">
                <h1><i class="fas fa-history"></i> Request History</h1>
                <p class="subtitle">View your approved and rejected transcript requests</p>
            </div>

            <!-- Category Selection -->
            <div class="category-selection">
                <h2>Select a category to view transcript requests:</h2>
                <div class="category-grid">
                    <div class="category-card approved" onclick="showCategory('approved')">
                        <div class="category-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3>Approved Requests</h3>
                        <span class="category-count">{{ approved_count }}</span>
                    </div>
                    
                    <div class="category-card rejected" onclick="showCategory('rejected')">
                        <div class="category-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <h3>Rejected Requests</h3>
                        <span class="category-count">{{ rejected_count }}</span>
                    </div>
                </div>
                <p class="category-instruction">Please select a category above to view the corresponding requests.</p>
            </div>

            <!-- Approved Requests Section -->
            <div id="approved-section" class="data-table" style="display: none;">
                <div class="table-header approved-header">
                    <h2><i class="fas fa-check-circle"></i> Approved Requests</h2>
                    <span>{{ approved_requests|length }} approved request(s)</span>
                </div>
                <div class="table-responsive">
                    {% if approved_requests %}
                    <table class="enhanced-table">
                        <thead>
                            <tr>
                                <th>Request ID</th>
                                <th>Date Submitted</th>
                                <th>Date Approved</th>
                                <th>Academic Years</th>
                                <th>Amount</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in approved_requests %}
                            <tr>
                                <td><strong>{{ request.request_number or request.id }}</strong></td>
                                <td>{{ request.date[:10] if request.date else 'N/A' }}</td>
                                <td>{{ request.approved_date[:10] if request.approved_date else 'N/A' }}</td>
                                <td>{{ request.academic_years|join(', ') if request.academic_years else 'N/A' }}</td>
                                <td class="amount-cell">{{ "{:,.0f}".format(request.total_amount) }} RWF</td>
                                <td>
                                    <span class="status-badge approved">
                                        <img src="{{ url_for('static', filename='images/approved.png') }}" alt="Approved" style="width: 16px; height: 16px;">
                                        {{ request.status|title }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <div class="no-data">
                        <i class="fas fa-check-circle"></i>
                        <h3>No Approved Requests</h3>
                        <p>You don't have any approved transcript requests yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Rejected Requests Section -->
            <div id="rejected-section" class="data-table" style="display: none;">
                <div class="table-header rejected-header">
                    <h2><i class="fas fa-times-circle"></i> Rejected Requests</h2>
                    <span>{{ rejected_requests|length }} rejected request(s)</span>
                </div>
                <div class="table-responsive">
                    {% if rejected_requests %}
                    <table class="enhanced-table">
                        <thead>
                            <tr>
                                <th>Request ID</th>
                                <th>Date Submitted</th>
                                <th>Date Rejected</th>
                                <th>Academic Years</th>
                                <th>Amount</th>
                                <th>Reason</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in rejected_requests %}
                            <tr>
                                <td><strong>{{ request.request_number or request.id }}</strong></td>
                                <td>{{ request.date[:10] if request.date else 'N/A' }}</td>
                                <td>{{ request.rejected_at[:10] if request.rejected_at else 'N/A' }}</td>
                                <td>{{ request.academic_years|join(', ') if request.academic_years else 'N/A' }}</td>
                                <td class="amount-cell">{{ "{:,.0f}".format(request.total_amount) }} RWF</td>
                                <td>{{ request.rejection_reason or 'No reason provided' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <div class="no-data">
                        <i class="fas fa-times-circle"></i>
                        <h3>No Rejected Requests</h3>
                        <p>You don't have any rejected transcript requests.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Enhanced Student Dashboard Styles */
.container-fluid {
    padding: 0;
    min-height: 100vh;
    background: #f5f7fa;
}

.sidebar {
    background: linear-gradient(135deg, #083464, #1976D2);
    color: white;
    min-height: 100vh;
    padding: 0;
}

.sidebar-header {
    text-align: center;
    padding: 30px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255, 255, 255, 0.3);
    margin-bottom: 15px;
}

.sidebar-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin: 5px 0;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: #4CAF50;
}

.sidebar-menu i {
    margin-right: 12px;
    width: 20px;
}

.main-content {
    padding: 30px;
}

.content-header {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.content-header h1 {
    color: #083464;
    font-size: 2rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
}

.category-selection {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    text-align: center;
}

.category-selection h2 {
    color: #083464;
    margin-bottom: 30px;
    font-size: 1.5rem;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.category-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.category-card.approved {
    border-color: #4CAF50;
}

.category-card.approved:hover {
    background: rgba(76, 175, 80, 0.05);
    border-color: #4CAF50;
}

.category-card.rejected {
    border-color: #F44336;
}

.category-card.rejected:hover {
    background: rgba(244, 67, 54, 0.05);
    border-color: #F44336;
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.category-card.approved .category-icon {
    color: #4CAF50;
}

.category-card.rejected .category-icon {
    color: #F44336;
}

.category-card h3 {
    color: #083464;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.category-count {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #083464;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.category-instruction {
    color: #666;
    font-style: italic;
    margin: 0;
}

.data-table {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.table-header {
    color: white;
    padding: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.approved-header {
    background: linear-gradient(135deg, #4CAF50, #388E3C);
}

.rejected-header {
    background: linear-gradient(135deg, #F44336, #D32F2F);
}

.table-header h2 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.table-responsive {
    overflow-x: auto;
}

.enhanced-table {
    width: 100%;
    border-collapse: collapse;
}

.enhanced-table th,
.enhanced-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.enhanced-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #083464;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.enhanced-table tr:hover {
    background: #f8f9fa;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.status-badge.approved {
    background: rgba(76, 175, 80, 0.1);
    color: #388E3C;
}

.amount-cell {
    font-weight: 700;
    color: #4CAF50;
}

.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        transition: left 0.3s ease;
        z-index: 1000;
        width: 280px;
    }

    .sidebar.active {
        left: 0;
    }

    .main-content {
        margin-left: 0;
        padding: 20px;
    }

    .category-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
function showCategory(category) {
    // Hide all sections
    document.getElementById('approved-section').style.display = 'none';
    document.getElementById('rejected-section').style.display = 'none';
    
    // Show selected section
    document.getElementById(category + '-section').style.display = 'block';
    
    // Update category card styles
    document.querySelectorAll('.category-card').forEach(card => {
        card.style.transform = 'none';
        card.style.boxShadow = 'none';
    });
    
    // Highlight selected category
    const selectedCard = document.querySelector('.category-card.' + category);
    selectedCard.style.transform = 'translateY(-5px)';
    selectedCard.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';
}
</script>
{% endblock %}
