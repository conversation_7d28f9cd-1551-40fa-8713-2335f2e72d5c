#!/usr/bin/env python3
"""
Test script to check for duplicate issues in finance dashboard
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from new_database_service import get_finance_dashboard_data

def test_finance_dashboard_data():
    """Test the finance dashboard data for duplicates"""
    print("🔍 Testing Finance Dashboard Data...")
    
    try:
        # Get dashboard data
        dashboard_data = get_finance_dashboard_data()
        
        print(f"\n📊 Dashboard Data Summary:")
        print(f"   Pending Requests: {len(dashboard_data['pending_requests'])}")
        print(f"   Approved Requests: {len(dashboard_data['approved_requests'])}")
        print(f"   Rejected Requests: {len(dashboard_data['rejected_requests'])}")
        print(f"   Pending Count: {dashboard_data['pending_count']}")
        print(f"   Approved Count: {dashboard_data['approved_count']}")
        print(f"   Rejected Count: {dashboard_data['rejected_count']}")
        print(f"   Total Count: {dashboard_data['total_count']}")
        
        # Check for duplicates in pending requests
        if dashboard_data['pending_requests']:
            print(f"\n📋 Pending Requests Details:")
            request_ids = []
            for i, req in enumerate(dashboard_data['pending_requests']):
                print(f"   {i+1}. ID: {req['id']}, Student: {req['student_name']}, Amount: {req['total_amount']}")
                request_ids.append(req['id'])
            
            # Check for duplicate IDs
            unique_ids = set(request_ids)
            if len(request_ids) != len(unique_ids):
                print(f"❌ DUPLICATE FOUND! {len(request_ids)} requests but only {len(unique_ids)} unique IDs")
                duplicates = [id for id in request_ids if request_ids.count(id) > 1]
                print(f"   Duplicate IDs: {set(duplicates)}")
            else:
                print(f"✅ No duplicates found in pending requests")
        else:
            print(f"\n📋 No pending requests found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing finance dashboard data: {e}")
        return False

if __name__ == "__main__":
    test_finance_dashboard_data()
